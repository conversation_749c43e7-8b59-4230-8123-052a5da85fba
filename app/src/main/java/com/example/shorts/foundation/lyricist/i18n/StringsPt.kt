package com.example.shorts.foundation.lyricist.i18n

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import cafe.adriel.lyricist.LyricistStrings
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.lyricist.AppStrings
import com.example.shorts.foundation.lyricist.Locales

@LyricistStrings(languageTag = Locales.PT, default = false)
val AppStringsPt = AppStrings(
  ok = "OK",
  appName = "DramaFix", // Keep unchanged as requested
  splashLoadingText = "Nada aqui por enquanto...",
  withdrawalNoReachTipsTemplate = "De acordo com os requisitos da plataforma, o valor mínimo de saque é {minAmount}. Você precisa de mais {needMore} para atingir o limite mínimo de saque.",
  withdrawalNoReachTips = { minAmount, needMore, highlightColor ->
    buildAnnotatedString {
      val template = "De acordo com os requisitos da plataforma, o valor mínimo de saque é {minAmount}. Você precisa de mais {needMore} para atingir o limite mínimo de saque."
      var lastIndex = 0

      // Find and replace {minAmount}
      val minAmountIndex = template.indexOf("{minAmount}")
      if (minAmountIndex != -1) {
        append(template.substring(lastIndex, minAmountIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(minAmount)
        }
        lastIndex = minAmountIndex + "{minAmount}".length
      }

      // Find and replace {needMore}
      val needMoreIndex = template.indexOf("{needMore}", lastIndex)
      if (needMoreIndex != -1) {
        append(template.substring(lastIndex, needMoreIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(needMore)
        }
        lastIndex = needMoreIndex + "{needMore}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  selectWithdrawalAmount = "Selecionar valor de saque",
  myWallet = "Minha Carteira",
  processing = "Processando...",
  cashOut = "Sacar",
  congratsWithdrawalTemplate = "🎉 Parabéns {userName} por sacar com sucesso {amount}",
  congratsWithdrawal = { userName, displayAmount, highlightColor ->
    buildAnnotatedString {
      val template = "🎉 Parabéns {userName} por sacar com sucesso {amount}"
      var lastIndex = 0

      // Find and replace {userName}
      val userNameIndex = template.indexOf("{userName}")
      if (userNameIndex != -1) {
        append(template.substring(lastIndex, userNameIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(userName)
        }
        lastIndex = userNameIndex + "{userName}".length
      }

      // Find and replace {amount}
      val amountIndex = template.indexOf("{amount}", lastIndex)
      if (amountIndex != -1) {
        append(template.substring(lastIndex, amountIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(displayAmount)
        }
        lastIndex = amountIndex + "{amount}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  importantNotice = "Aviso Importante",
  noticeItem1 = "1. Após ganhar tokens no jogo, você pode visitar a página de Saque para verificar seu saldo e solicitar um saque.",
  noticeItem2 = "2. Todas as recompensas em dinheiro serão transferidas através do método de pagamento especificado.",
  noticeItem3 = "3. Você deve atender às condições para cada valor de saque antes de solicitar.",
  noticeItem4 = "4. Solicitações de saque são geralmente processadas em até 7 dias.",
  noticeItem5 = "5. Erros nas informações de pagamento podem ser corrigidos entrando em contato conosco, mas os usuários são responsáveis por quaisquer problemas causados por detalhes incorretos.",
  noticeItem6 = "6. Os usuários são os únicos responsáveis por quaisquer impostos incorridos. Reservamo-nos o direito de reter impostos aplicáveis conforme exigido por lei.",
  noticeItem7 = "7. Se as recompensas não puderem ser emitidas por qualquer motivo, reservamo-nos o direito de retê-las.",
  editInformation = "Editar Informações",
  paypalDetailsPrompt = "Por favor, complete os seguintes detalhes para habilitar o saque.",
  enterPaypalAccountPlaceholder = "Digite sua conta PayPal aqui",
  invalidPaypalAccountError = "Por favor, digite uma conta PayPal válida (email, telefone, @usuário, ou link paypal.me)",
  confirm = "Confirmar",
  confirmWithdrawal = "Confirmar Saque",
  withdrawalConfirmationTemplate = "Você está prestes a sacar {amount} para sua conta PayPal {email}. Por favor, confirme para continuar.",
  withdrawalConfirmationText = { amount, paypalEmail ->
    buildAnnotatedString {
      val template = "Você está prestes a sacar {amount} para sua conta PayPal {email}. Por favor, confirme para continuar."
      var lastIndex = 0

      // Find and replace {amount}
      val amountIndex = template.indexOf("{amount}")
      if (amountIndex != -1) {
        append(template.substring(lastIndex, amountIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(amount)
        }
        lastIndex = amountIndex + "{amount}".length
      }

      // Find and replace {email}
      val emailIndex = template.indexOf("{email}", lastIndex)
      if (emailIndex != -1) {
        append(template.substring(lastIndex, emailIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(paypalEmail)
        }
        lastIndex = emailIndex + "{email}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  play = "Reproduzir",
  back = "Voltar",
  withdraw = "Sacar",
  episodes = "Episódios",
  selectEpisode = "Selecionar Episódio",
  close = "Fechar",
  totalEpisodesTemplate = "Total de {total} episódios",
  totalEpisodes = { total -> "Total de ${total} episódios" },
  nowPlaying = "Reproduzindo Agora",
  watchXAds = { x -> "Assistir $x Anúncios" },
  watchXEpDrama = { x -> "Assistir drama de $x episódios" },
  withdrawalSuccessfulTemplate = "Sua solicitação de saque de {amount} foi enviada. Será processada em até {processingTime}.",
  withdrawalSuccessfulText = { amount ->
    buildAnnotatedString {
      val template = "Sua solicitação de saque de {amount} foi enviada. Será processada em até {processingTime}."
      val processingTime = "7 dias úteis"
      var lastIndex = 0

      // Find and replace {amount}
      val amountIndex = template.indexOf("{amount}")
      if (amountIndex != -1) {
        append(template.substring(lastIndex, amountIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(amount)
        }
        lastIndex = amountIndex + "{amount}".length
      }

      // Find and replace {processingTime}
      val processingTimeIndex = template.indexOf("{processingTime}", lastIndex)
      if (processingTimeIndex != -1) {
        append(template.substring(lastIndex, processingTimeIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(processingTime)
        }
        lastIndex = processingTimeIndex + "{processingTime}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  recommendedForYou = "Recomendado para Você",
  newReleases = "Novos Lançamentos",
  categories = "Categorias",
  episodesCount = { count -> "$count Episódios" },
  more = "Mais",
  noMoreContent = "Não há mais conteúdo",
  category = "Categoria",
  retry = "Tentar Novamente",
  episodeRange = { total -> "EP.1 | EP.$total" },
  welcomeToShortplay = "Bem-vindo ao DramaFix",
  privacyPolicy = "Política de Privacidade",
  ageConsentMessageTemplate = "Queremos que você aproveite dramas curtos personalizados. Para continuar, por favor revise e concorde com nossa {privacyPolicy}. Este aplicativo é destinado a usuários com 18 anos de idade ou mais.",
  ageConsentMessage = { onPrivacyPolicyClick ->
    buildAnnotatedString {
      val template = "Queremos que você aproveite dramas curtos personalizados. Para continuar, por favor revise e concorde com nossa {privacyPolicy}. Este aplicativo é destinado a usuários com 18 anos de idade ou mais."
      val privacyPolicyTextPlace = "{privacyPolicy}"
      val startIndex = template.indexOf(privacyPolicyTextPlace)

      if (startIndex != -1) {
        // Add text before "Privacy Policy"
        append(template.substring(0, startIndex))

        // Add clickable "Privacy Policy" with LinkAnnotation.Clickable
        val linkAnnotation = LinkAnnotation.Clickable(
          tag = "privacy_policy",
          linkInteractionListener = { onPrivacyPolicyClick() }
        )

        withLink(linkAnnotation) {
          withStyle(
            style = SpanStyle(
              color = MaterialTheme.colorScheme.primary.brighten(),
              fontWeight = FontWeight.Medium
            )
          ) {
            append("Política de Privacidade")
          }
        }

        // Add text after "Privacy Policy"
        append(template.substring(startIndex + privacyPolicyTextPlace.length))
      } else {
        // Fallback if "Privacy Policy" not found
        append(template)
      }
    }
  },
  agree = "Concordar",
  pleaseAgreeToTerms = "Por favor, concorde com os termos para visualizar o conteúdo",
  agreeToTermsDescription = "Você precisa concordar com nossa Política de Privacidade e Termos de Uso para acessar o conteúdo de vídeo.",

  // Bottom Navigation
  discover = "Descobrir",
  shorts = "Shorts",
  task = "Tarefa",

  // Check In
  checkIn = "Check-in",
  dayTemplate = "Dia{day}",
  day = { day -> "Dia$day" },

  // Tasks
  moreTasks = "Mais Tarefas",
  taskButton30s = "30s",
  taskButtonWatch = "Assistir",
  taskButtonGo = "Ir",
)
