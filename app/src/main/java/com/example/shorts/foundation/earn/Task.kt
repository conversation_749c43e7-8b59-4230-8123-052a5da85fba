package com.example.shorts.foundation.earn

import android.os.Parcelable
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.shorts.foundation.lyricist.globalAppStrings
import com.example.shorts.foundation.number.formatWithCommas
import com.example.shorts.ui.icon.AdIcon
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.VideoIcon
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class Reward(
  val amount: BigDecimal,
  val currencySymbol: String = "$"
) : Parcelable {
  @IgnoredOnParcel
  val amountWithCommas get() = amount.formatWithCommas()

  @IgnoredOnParcel
  val displayAmount get() = "${currencySymbol}${amountWithCommas}"
}

sealed interface TaskType : Parcelable {
  @Parcelize
  data object WatchAds : TaskType

  @Parcelize
  data object WatchDrama : TaskType
}

@Parcelize
data class TaskTemplate(
  val id: String,
  val type: TaskType,
  val goal: Int,
  val reward: Reward,
  val buttonText: String
) : Parcelable {
  @IgnoredOnParcel
  val icon: ImageVector
    get() = when (type) {
      TaskType.WatchAds -> ValkyrieIcons.AdIcon
      TaskType.WatchDrama -> ValkyrieIcons.VideoIcon
    }

  @IgnoredOnParcel
  val title: String
    get() = when (type) {
      TaskType.WatchAds -> globalAppStrings.watchXAds(goal)
      TaskType.WatchDrama -> globalAppStrings.watchXEpDrama(goal)
    }
}

@Parcelize
data class TaskInstance(
  val templateId: String,
  val progress: Int,
  val enabled: Boolean = true,
  val rewardClaimed: Boolean = false
) : Parcelable

@Parcelize
data class Task(
  val template: TaskTemplate,
  val instance: TaskInstance
) : Parcelable {

  @IgnoredOnParcel
  val type: TaskType get() = template.type

  @IgnoredOnParcel
  val title: String get() = template.title

  @IgnoredOnParcel
  val progress: Int get() = instance.progress

  @IgnoredOnParcel
  val goal: Int get() = template.goal

  @IgnoredOnParcel
  val reward: Reward get() = template.reward

  @IgnoredOnParcel
  val buttonText: String get() = template.buttonText

  @IgnoredOnParcel
  val enabled: Boolean get() = instance.enabled

  @IgnoredOnParcel
  val rewardClaimed: Boolean get() = instance.rewardClaimed

  @IgnoredOnParcel
  val icon: ImageVector get() = template.icon

  @IgnoredOnParcel
  val subtitle: String get() = if (progress > goal) "(${goal}/${goal})" else "(${progress}/${goal})"

  @IgnoredOnParcel
  val isCompleted: Boolean get() = progress >= goal

  @IgnoredOnParcel
  val canClaimReward: Boolean get() = isCompleted && !rewardClaimed
}

val TaskTemplates = listOf(
  TaskTemplate("watch_ads_3", TaskType.WatchAds, 3, Reward(BigDecimal("100")), globalAppStrings.taskButton30s),
  TaskTemplate("watch_ads_10", TaskType.WatchAds, 10, Reward(BigDecimal("100")), globalAppStrings.taskButtonWatch),
  TaskTemplate("watch_ads_20", TaskType.WatchAds, 20, Reward(BigDecimal("100")), globalAppStrings.taskButtonWatch),
  TaskTemplate("watch_drama_15", TaskType.WatchDrama, 15, Reward(BigDecimal("100")), globalAppStrings.taskButtonGo),
  TaskTemplate("watch_drama_30", TaskType.WatchDrama, 30, Reward(BigDecimal("100")), globalAppStrings.taskButtonGo),
  TaskTemplate("watch_drama_50", TaskType.WatchDrama, 50, Reward(BigDecimal("500")), globalAppStrings.taskButtonGo),
  TaskTemplate("watch_drama_80", TaskType.WatchDrama, 80, Reward(BigDecimal("200")), globalAppStrings.taskButtonGo),
  TaskTemplate("watch_drama_100", TaskType.WatchDrama, 100, Reward(BigDecimal("200")), globalAppStrings.taskButtonGo),
  TaskTemplate("watch_drama_200", TaskType.WatchDrama, 200, Reward(BigDecimal("2000")), globalAppStrings.taskButtonGo)
)
