package com.example.shorts.foundation.earn

import android.os.Parcelable
import com.example.shorts.foundation.number.formatWithCommas
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal
import java.util.Locale

@Parcelize
data class Withdrawal(
  val amount: BigDecimal,
  val currencySymbol: String
) : Parcelable {
  @IgnoredOnParcel
  val amountWithCommas get() = amount.formatWithCommas()

  @IgnoredOnParcel
  val displayAmount get() = "${currencySymbol}${amountWithCommas}"
}

// 美元提现选项
private val WithdrawalOptionsUSD = listOf(
  Withdrawal(BigDecimal(7000), Currency.USD.symbol),
  Withdrawal(BigDecimal(12000), Currency.USD.symbol),
  Withdrawal(BigDecimal(20000), Currency.USD.symbol),
  Withdrawal(BigDecimal(30000), Currency.USD.symbol),
  Withdrawal(BigDecimal(46000), Currency.USD.symbol)
)

// 巴西雷亚尔提现选项 (按当前汇率约5.0转换为整数)
private val WithdrawalOptionsBRL = listOf(
  Withdrawal(BigDecimal(35000), Currency.BRL.symbol),   // $7000 * 5.0 = R$35,000
  Withdrawal(BigDecimal(60000), Currency.BRL.symbol),   // $12000 * 5.0 = R$60,000
  Withdrawal(BigDecimal(100000), Currency.BRL.symbol),  // $20000 * 5.0 = R$100,000
  Withdrawal(BigDecimal(150000), Currency.BRL.symbol),  // $30000 * 5.0 = R$150,000
  Withdrawal(BigDecimal(230000), Currency.BRL.symbol)   // $46000 * 5.0 = R$230,000
)

/**
 * 根据地区获取提现选项
 * @param locale 地区字符串，如 "en-US", "pt-BR"
 * @return 对应地区的提现选项列表
 */
fun getWithdrawalOptions(locale: Locale? = null): List<Withdrawal> {
  val currency = Currency.fromLocale(locale)
  return when (currency) {
    Currency.USD -> WithdrawalOptionsUSD
    Currency.BRL -> WithdrawalOptionsBRL
  }
}

// 保持向后兼容性
val WithdrawalOptions = WithdrawalOptionsUSD