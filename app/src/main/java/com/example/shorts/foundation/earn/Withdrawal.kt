package com.example.shorts.foundation.earn

import android.os.Parcelable
import com.example.shorts.foundation.number.formatWithCommas
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class Withdrawal(
  val amount: BigDecimal,
  val currencySymbol: String
) : Parcelable {
  @IgnoredOnParcel
  val amountWithCommas get() = amount.formatWithCommas()

  @IgnoredOnParcel
  val displayAmount get() = "${currencySymbol}${amountWithCommas}"
}

// 美元提现选项
private val WithdrawalOptionsUSD = listOf(
  Withdrawal(BigDecimal(7000), Currency.USD.symbol),
  Withdrawal(BigDecimal(12000), Currency.USD.symbol),
  Withdrawal(BigDecimal(20000), Currency.USD.symbol),
  Withdrawal(BigDecimal(30000), Currency.USD.symbol),
  Withdrawal(BigDecimal(46000), Currency.USD.symbol)
)

// 巴西雷亚尔提现选项 (基于美元金额转换)
private val WithdrawalOptionsBRL = listOf(
  Withdrawal(CurrencyConverter.convertFromUSD(BigDecimal(7000), Currency.BRL), Currency.BRL.symbol),
  Withdrawal(CurrencyConverter.convertFromUSD(BigDecimal(12000), Currency.BRL), Currency.BRL.symbol),
  Withdrawal(CurrencyConverter.convertFromUSD(BigDecimal(20000), Currency.BRL), Currency.BRL.symbol),
  Withdrawal(CurrencyConverter.convertFromUSD(BigDecimal(30000), Currency.BRL), Currency.BRL.symbol),
  Withdrawal(CurrencyConverter.convertFromUSD(BigDecimal(46000), Currency.BRL), Currency.BRL.symbol)
)

/**
 * 根据地区获取提现选项
 * @param locale 地区字符串，如 "en-US", "pt-BR"
 * @return 对应地区的提现选项列表
 */
fun getWithdrawalOptions(locale: String? = null): List<Withdrawal> {
  val currency = Currency.fromLocale(locale)
  return when (currency) {
    Currency.USD -> WithdrawalOptionsUSD
    Currency.BRL -> WithdrawalOptionsBRL
  }
}

// 保持向后兼容性
val WithdrawalOptions = WithdrawalOptionsUSD