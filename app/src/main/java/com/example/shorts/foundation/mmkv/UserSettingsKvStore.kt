package com.example.shorts.foundation.mmkv

import android.content.Context
import com.tencent.mmkv.MMKV
import org.koin.core.annotation.Single

@Single
class UserSettingsKvStore(context: Context) {
  companion object {
    const val TAG = "UserSettingsKvStore"
  }

  private val mmkv by lazy { MMKV.mmkvWithID(TAG) }

  val enabledNotification = mmkv.getBooleanStateFlow(
    key = "enabled_notification",
    defaultValue = true
  )

  val agreedToAgeConsent = mmkv.getBooleanStateFlow(
    key = "agreed_to_age_consent",
    defaultValue = false
  )
}
