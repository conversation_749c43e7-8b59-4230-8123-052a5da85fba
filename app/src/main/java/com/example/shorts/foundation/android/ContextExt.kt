package com.example.shorts.foundation.android

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.net.Uri
import androidx.core.net.toUri

fun Context.findActivity(): Activity {
  var context = this
  while (context is ContextWrapper) {
    if (context is Activity) return context
    context = context.baseContext
  }
  throw IllegalStateException("CAN NOT FIND ANY ACTIVITY")
}

fun Context.openUrlInBrowser(url: String) {
  try {
    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
    startActivity(intent)
  } catch (e: Exception) {
    // Handle case where no browser is available
    e.printStackTrace()
  }
}