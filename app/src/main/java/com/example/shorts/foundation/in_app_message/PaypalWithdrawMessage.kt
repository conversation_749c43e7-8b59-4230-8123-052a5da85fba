package com.example.shorts.foundation.in_app_message

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.LineBreak
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.R
import com.example.shorts.foundation.android.brighten
import com.example.shorts.res.AppRes
import com.example.shorts.res.namePool
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.earn.getWithdrawalOptions
import com.example.shorts.foundation.lyricist.runtimeLanguageTagFlow
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal

@SuppressLint("ModifierParameter")
@Composable
fun PaypalWithdrawMessage(
  userName: String = AppRes.namePool.random(),
  withdrawal: Withdrawal? = null,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  val currentLocale by runtimeLanguageTagFlow.collectAsState()
  val withdrawalOptions = remember(currentLocale) { getWithdrawalOptions(currentLocale) }
  val actualWithdrawal = withdrawal ?: withdrawalOptions.random()

  Box(modifier) {
    Card(
      modifier = Modifier.padding(12.dp)
    ) {
      Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier
          .fillMaxWidth()
          .padding(16.dp)
      ) {
        Image(
          painter = painterResource(id = R.drawable.img_paypal_2),
          contentDescription = "PayPal",
          modifier = Modifier.size(56.dp)
        )

        Text(
          text = strings.congratsWithdrawal(
            userName,
            actualWithdrawal.displayAmount,
            MaterialTheme.colorScheme.primary.brighten()
          ),
          style = MaterialTheme.typography.titleSmall.copy(
            lineBreak = LineBreak.Paragraph.copy(strategy = LineBreak.Strategy.Balanced)
          ),
        )
      }
    }
  }
}


@Preview
@Composable
private fun PaypalWithdrawMessagePreview() {
  AppTheme {
    PaypalWithdrawMessage(
      userName = AppRes.namePool.random(),
      withdrawal = Withdrawal(BigDecimal("123.45"), "$")
    )
  }
}