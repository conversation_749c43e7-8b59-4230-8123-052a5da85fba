package com.example.shorts.foundation.checkin

import android.os.Parcelable
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.shorts.foundation.earn.Currency
import com.example.shorts.foundation.earn.CurrencyConverter
import com.example.shorts.foundation.earn.Reward
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal
import java.time.LocalDate

@Parcelize
data class CheckInReward(
  val day: Int,
  val reward: Reward
) : Parcelable

sealed class CheckInResult {
  data class Success(
    val day: Int,
    val reward: CheckInReward,
    val isNewCycle: Boolean = false
  ) : CheckInResult()

  object AlreadyCheckedIn : CheckInResult()
}

sealed class CheckInGridItem {
  data class CheckInDay(
    val day: Int,
    val reward: CheckInReward,
    val isChecked: Boolean,
    val isToday: Boolean
  ) : CheckInGridItem()

  data class ExternalLink(
    val icon: ImageVector,
    val onClick: () -> Unit = {}
  ) : CheckInGridItem()
}

data class CheckInState(
  val consecutiveDays: Int,
  val todayCheckedIn: Boolean,
  val lastCheckInDate: LocalDate?,
  val currentDay: Int
)

// 美元签到奖励
private val DefaultCheckInRewardsUSD = listOf(
  CheckInReward(1, Reward(BigDecimal("100"), Currency.USD.symbol)),
  CheckInReward(2, Reward(BigDecimal("200"), Currency.USD.symbol)),
  CheckInReward(3, Reward(BigDecimal("160"), Currency.USD.symbol)),
  CheckInReward(4, Reward(BigDecimal("100"), Currency.USD.symbol)),
  CheckInReward(5, Reward(BigDecimal("2000"), Currency.USD.symbol)),
  CheckInReward(6, Reward(BigDecimal("2000"), Currency.USD.symbol)),
  CheckInReward(7, Reward(BigDecimal("6000"), Currency.USD.symbol))
)

// 巴西雷亚尔签到奖励 (基于美元金额转换)
private val DefaultCheckInRewardsBRL = listOf(
  CheckInReward(1, Reward(CurrencyConverter.convertFromUSD(BigDecimal("100"), Currency.BRL), Currency.BRL.symbol)),
  CheckInReward(2, Reward(CurrencyConverter.convertFromUSD(BigDecimal("200"), Currency.BRL), Currency.BRL.symbol)),
  CheckInReward(3, Reward(CurrencyConverter.convertFromUSD(BigDecimal("160"), Currency.BRL), Currency.BRL.symbol)),
  CheckInReward(4, Reward(CurrencyConverter.convertFromUSD(BigDecimal("100"), Currency.BRL), Currency.BRL.symbol)),
  CheckInReward(5, Reward(CurrencyConverter.convertFromUSD(BigDecimal("2000"), Currency.BRL), Currency.BRL.symbol)),
  CheckInReward(6, Reward(CurrencyConverter.convertFromUSD(BigDecimal("2000"), Currency.BRL), Currency.BRL.symbol)),
  CheckInReward(7, Reward(CurrencyConverter.convertFromUSD(BigDecimal("6000"), Currency.BRL), Currency.BRL.symbol))
)

/**
 * 根据地区获取签到奖励
 * @param locale 地区字符串，如 "en-US", "pt-BR"
 * @return 对应地区的签到奖励列表
 */
fun getCheckInRewards(locale: String? = null): List<CheckInReward> {
  val currency = Currency.fromLocale(locale)
  return when (currency) {
    Currency.USD -> DefaultCheckInRewardsUSD
    Currency.BRL -> DefaultCheckInRewardsBRL
  }
}

// 保持向后兼容性
val DefaultCheckInRewards = DefaultCheckInRewardsUSD

interface CheckInRepository {
  fun getCheckInState(): CheckInState
  fun checkIn(): CheckInResult
  fun getCheckInRewards(): List<CheckInReward>
}