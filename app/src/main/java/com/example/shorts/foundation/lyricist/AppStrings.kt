package com.example.shorts.foundation.lyricist

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import cafe.adriel.lyricist.Strings
import com.example.shorts.LocaleSupport
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.lyricist.i18n.AppStringsEn
import com.example.shorts.foundation.lyricist.i18n.AppStringsPt
import kotlinx.coroutines.flow.MutableStateFlow

object Locales {
  const val EN = "en"
  const val PT = "pt"

}

val runtimeLanguageTagFlow = MutableStateFlow(Locale.current.toLanguageTag())

@Suppress("ObjectPropertyName")
private var _globalAppStrings: AppStrings? = null
val globalAppStrings get() = _globalAppStrings ?: AppStringsEn

fun configureGlobalStrings(appStrings: AppStrings) {
  _globalAppStrings = appStrings
}

@Composable
fun ConfigureGlobalStringsEffect(appStrings: AppStrings) {
  LaunchedEffect(appStrings) {
    configureGlobalStrings(appStrings)
  }
}

fun matchStrings(languageTag: String): AppStrings {
  val compatibleLocale: java.util.Locale =
    LocaleSupport.compatibleLanguage(java.util.Locale.forLanguageTag(languageTag))
      ?: LocaleSupport.En

  return when (compatibleLocale.toLanguageTag()) {
    Locales.PT -> AppStringsPt
    else -> Strings[compatibleLocale.toLanguageTag()] ?: AppStringsEn
  }
}

data class AppStrings(
  val ok: String = "OK",
  val appName: String = "DramaFix",
  val splashLoadingText: String = "Nothing here for now...",
  val withdrawalNoReachTipsTemplate: String = "According to the platform requirements, the minimum withdrawal amount is {minAmount}. You need {needMore} more to reach the minimum withdrawal threshold.",
  val withdrawalNoReachTips: (minAmount: String, needMore: String, highlightColor: Color) -> AnnotatedString = { minAmount, needMore, highlightColor ->
    buildAnnotatedString {
      val template = withdrawalNoReachTipsTemplate
      var lastIndex = 0

      // Find and replace {minAmount}
      val minAmountIndex = template.indexOf("{minAmount}")
      if (minAmountIndex != -1) {
        append(template.substring(lastIndex, minAmountIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(minAmount)
        }
        lastIndex = minAmountIndex + "{minAmount}".length
      }

      // Find and replace {needMore}
      val needMoreIndex = template.indexOf("{needMore}", lastIndex)
      if (needMoreIndex != -1) {
        append(template.substring(lastIndex, needMoreIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(needMore)
        }
        lastIndex = needMoreIndex + "{needMore}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  val selectWithdrawalAmount: String = "Select withdrawal amount",
  val myWallet: String = "My Wallet",
  val processing: String = "Processing...",
  val cashOut: String = "Cash out",
  val congratsWithdrawalTemplate: String = "🎉 Congrats {userName} on successfully withdrawing {amount}",
  val congratsWithdrawal: (userName: String, displayAmount: String, highlightColor: Color) -> AnnotatedString = { userName, displayAmount, highlightColor ->
    buildAnnotatedString {
      val template = congratsWithdrawalTemplate
      var lastIndex = 0

      // Find and replace {userName}
      val userNameIndex = template.indexOf("{userName}")
      if (userNameIndex != -1) {
        append(template.substring(lastIndex, userNameIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(userName)
        }
        lastIndex = userNameIndex + "{userName}".length
      }

      // Find and replace {amount}
      val amountIndex = template.indexOf("{amount}", lastIndex)
      if (amountIndex != -1) {
        append(template.substring(lastIndex, amountIndex))
        withStyle(
          style = SpanStyle(
            color = highlightColor,
            fontWeight = FontWeight.Bold
          )
        ) {
          append(displayAmount)
        }
        lastIndex = amountIndex + "{amount}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  val importantNotice: String = "Important Notice",
  val noticeItem1: String = "1. After earning tokens in the game, you can visit the Cash Out page to check your balance and apply for a withdrawal.",
  val noticeItem2: String = "2. All cash rewards will be transferred through the specified payment method.",
  val noticeItem3: String = "3. You must meet the conditions for each withdrawal amount before applying.",
  val noticeItem4: String = "4. Withdrawal requests are usually processed within 7 days.",
  val noticeItem5: String = "5. Errors in payment information can be corrected by contacting us, but users are responsible for any issues caused by incorrect details.",
  val noticeItem6: String = "6. Users are solely responsible for any taxes incurred. We reserve the right to withhold applicable taxes as required by law.",
  val noticeItem7: String = "7. If rewards cannot be issued for any reason, we reserve the right to withhold them.",
  val editInformation: String = "Edit Information",
  val paypalDetailsPrompt: String = "Please complete the following details to enable cash out.",
  val enterPaypalAccountPlaceholder: String = "Enter PayPal Account here",
  val invalidPaypalAccountError: String = "Please enter a valid PayPal account (email, phone, @username, or paypal.me link)",
  val confirm: String = "Confirm",
  val confirmWithdrawal: String = "Confirm Withdrawal",
  val withdrawalConfirmationTemplate: String = "You are about to withdraw {amount} to your PayPal account {email}. Please confirm to continue.",
  val withdrawalConfirmationText: @Composable (amount: String, paypalEmail: String) -> AnnotatedString = { amount, paypalEmail ->
    buildAnnotatedString {
      val template = withdrawalConfirmationTemplate
      var lastIndex = 0

      // Find and replace {amount}
      val amountIndex = template.indexOf("{amount}")
      if (amountIndex != -1) {
        append(template.substring(lastIndex, amountIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(amount)
        }
        lastIndex = amountIndex + "{amount}".length
      }

      // Find and replace {email}
      val emailIndex = template.indexOf("{email}", lastIndex)
      if (emailIndex != -1) {
        append(template.substring(lastIndex, emailIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(paypalEmail)
        }
        lastIndex = emailIndex + "{email}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  val play: String = "Play",
  val back: String = "Back",
  val withdraw: String = "Withdraw",
  val episodes: String = "Episodes",
  val selectEpisode: String = "Select Episode",
  val close: String = "Close",
  val totalEpisodesTemplate: String = "Total {total} episodes",
  val totalEpisodes: (Int) -> String = { total -> totalEpisodesTemplate.replace("{total}", total.toString()) },
  val nowPlaying: String = "Now Playing",
  val watchXAds: (Int) -> String = { x -> "Watch $x Ads" },
  val watchXEpDrama: (Int) -> String = { x -> "Watch $x-EP drama" },
  val withdrawalSuccessfulTemplate: String = "Your withdrawal request of {amount} has been submitted. It will be processed within {processingTime}.",
  val withdrawalSuccessfulText: @Composable (amount: String) -> AnnotatedString = { amount ->
    buildAnnotatedString {
      val template = withdrawalSuccessfulTemplate
      val processingTime = "7 business days"
      var lastIndex = 0

      // Find and replace {amount}
      val amountIndex = template.indexOf("{amount}")
      if (amountIndex != -1) {
        append(template.substring(lastIndex, amountIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(amount)
        }
        lastIndex = amountIndex + "{amount}".length
      }

      // Find and replace {processingTime}
      val processingTimeIndex = template.indexOf("{processingTime}", lastIndex)
      if (processingTimeIndex != -1) {
        append(template.substring(lastIndex, processingTimeIndex))
        withStyle(
          style = SpanStyle(
            color = MaterialTheme.colorScheme.primary.brighten(),
            fontWeight = FontWeight.Bold
          )
        ) {
          append(processingTime)
        }
        lastIndex = processingTimeIndex + "{processingTime}".length
      }

      // Append remaining text
      if (lastIndex < template.length) {
        append(template.substring(lastIndex))
      }
    }
  },
  val recommendedForYou: String = "Recommended for you",
  val newReleases: String = "New Releases",
  val categories: String = "Categories",
  val episodesCount: (Int) -> String = { count -> "$count Episodes" },
  val more: String = "More",
  val noMoreContent: String = "No more content",
  val category: String = "Category",
  val retry: String = "Retry",
  val episodeRange: (Int) -> String = { total -> "EP.1 | EP.$total" },
  val welcomeToShortplay: String = "Welcome to $appName",
  val privacyPolicy: String = "Privacy Policy",
  val ageConsentMessageTemplate: String = "We want you to enjoy personalized short dramas. To continue, please review and agree to our {privacyPolicy}. This application is intended for users who are 18 years of age or older.",
  val ageConsentMessage: @Composable (onPrivacyPolicyClick: () -> Unit) -> AnnotatedString = { onPrivacyPolicyClick ->
    buildAnnotatedString {
      val template = ageConsentMessageTemplate
      val privacyPolicyTextPlace = "{privacyPolicy}"
      val startIndex = template.indexOf(privacyPolicyTextPlace)

      if (startIndex != -1) {
        // Add text before "Privacy Policy"
        append(template.substring(0, startIndex))

        // Add clickable "Privacy Policy" with LinkAnnotation.Clickable
        val linkAnnotation = LinkAnnotation.Clickable(
          tag = "privacy_policy",
          linkInteractionListener = { onPrivacyPolicyClick() }
        )

        withLink(linkAnnotation) {
          withStyle(
            style = SpanStyle(
              color = MaterialTheme.colorScheme.primary.brighten(),
              fontWeight = FontWeight.Medium
            )
          ) {
            append(privacyPolicy)
          }
        }

        // Add text after "Privacy Policy"
        append(template.substring(startIndex + privacyPolicyTextPlace.length))
      } else {
        // Fallback if "Privacy Policy" not found
        append(template)
      }
    }
  },
  val agree: String = "Agree",
  val pleaseAgreeToTerms: String = "Please agree to the terms to view content",
  val agreeToTermsDescription: String = "You need to agree to our Privacy Policy and Terms of Use to access video content.",
)