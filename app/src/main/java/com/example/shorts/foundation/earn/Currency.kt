package com.example.shorts.foundation.earn

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Locale

/**
 * 支持的货币类型
 */
@Parcelize
enum class Currency(
  val symbol: String,
  val code: String,
  val countryCode: String
) : Parcelable {
  USD("$", "USD", "US"),
  BRL("R$", "BRL", "BR");
  
  companion object {
    /**
     * 根据国家代码获取货币
     * @param countryCode 国家代码，如 "US", "BR"
     * @return 对应的货币，默认返回USD
     */
    fun fromCountryCode(countryCode: String?): Currency {
      return when (countryCode?.uppercase()) {
        "BR" -> BRL
        else -> USD
      }
    }
    
    /**
     * 根据地区字符串获取货币
     * @param locale 地区字符串，如 "en-US", "pt-BR"
     * @return 对应的货币，默认返回USD
     */
    fun fromLocale(locale: Locale?): Currency {
      return fromCountryCode(locale?.country)
    }
  }
}


