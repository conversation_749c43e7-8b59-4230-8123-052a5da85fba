package com.example.shorts.foundation.earn

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 支持的货币类型
 */
@Parcelize
enum class Currency(
  val symbol: String,
  val code: String,
  val countryCode: String
) : Parcelable {
  USD("$", "USD", "US"),
  BRL("R$", "BRL", "BR");
  
  companion object {
    /**
     * 根据国家代码获取货币
     * @param countryCode 国家代码，如 "US", "BR"
     * @return 对应的货币，默认返回USD
     */
    fun fromCountryCode(countryCode: String?): Currency {
      return when (countryCode?.uppercase()) {
        "BR" -> BRL
        else -> USD
      }
    }
    
    /**
     * 根据地区字符串获取货币
     * @param locale 地区字符串，如 "en-US", "pt-BR"
     * @return 对应的货币，默认返回USD
     */
    fun fromLocale(locale: String?): Currency {
      if (locale.isNullOrBlank()) return USD
      
      // 提取国家代码部分
      val countryCode = when {
        locale.contains("-BR", ignoreCase = true) -> "BR"
        locale.contains("BR", ignoreCase = true) -> "BR"
        locale.startsWith("pt", ignoreCase = true) -> "BR" // 葡萄牙语默认巴西
        else -> null
      }
      
      return fromCountryCode(countryCode)
    }
  }
}

/**
 * 货币转换工具
 */
object CurrencyConverter {
  // 美元到巴西雷亚尔的汇率 (示例汇率，实际应用中应该从API获取)
  private const val USD_TO_BRL_RATE = 5.0
  
  /**
   * 将美元金额转换为指定货币
   * @param usdAmount 美元金额
   * @param targetCurrency 目标货币
   * @return 转换后的金额
   */
  fun convertFromUSD(usdAmount: Double, targetCurrency: Currency): Double {
    return when (targetCurrency) {
      Currency.USD -> usdAmount
      Currency.BRL -> usdAmount * USD_TO_BRL_RATE
    }
  }
  
  /**
   * 将美元金额转换为指定货币（BigDecimal版本）
   */
  fun convertFromUSD(usdAmount: java.math.BigDecimal, targetCurrency: Currency): java.math.BigDecimal {
    return when (targetCurrency) {
      Currency.USD -> usdAmount
      Currency.BRL -> usdAmount.multiply(java.math.BigDecimal.valueOf(USD_TO_BRL_RATE))
    }
  }
}
