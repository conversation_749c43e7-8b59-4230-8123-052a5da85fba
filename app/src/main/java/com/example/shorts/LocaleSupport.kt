package com.example.shorts

import java.util.Locale
import kotlin.collections.forEach
import kotlin.text.isEmpty
import kotlin.text.isNotEmpty

@Suppress("MemberVisibilityCanBePrivate", "HasPlatformType")
object LocaleSupport {
  val En = Locale.ENGLISH
  val Pt = Locale("pt")

  val localeList = listOf<Locale>(
    En,
    Pt,
  )

  val latinsExcludeEn = listOf<Locale>(
    Pt,
  )

  val languageMatchList: List<String>
    get() {
      val matches = mutableListOf<String>()

      localeList.forEach {
        if (it.language.isNotEmpty() && it.country.isEmpty()) {
          matches.add(it.language)
        }
      }

      return matches
    }

  fun compatibleLanguage(locale: Locale): Locale? {
    return if (locale.language in languageMatchList) {
      Locale(locale.language)
    } else if (locale in localeList) {
      locale
    } else {
      null
    }
  }
}

fun Locale.localeLanguage(): String {
  return buildString {
    append(getDisplayLanguage(this@localeLanguage))
    val localeRegion = getDisplayCountry(this@localeLanguage)
    if (localeRegion.trim().isNotEmpty()) {
      append(" - $localeRegion")
    }
  }
}

fun Locale.localeEmoji(): String {
  return when (this) {
    LocaleSupport.En -> "🇺🇸"
    LocaleSupport.Pt -> "🇧🇷"
    else -> "🌐"
  }
}
