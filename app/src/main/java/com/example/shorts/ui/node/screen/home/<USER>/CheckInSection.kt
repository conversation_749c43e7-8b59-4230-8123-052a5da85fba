package com.example.shorts.ui.node.screen.home.tasks

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.R
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.checkin.CheckInGridItem
import com.example.shorts.foundation.checkin.CheckInReward
import com.example.shorts.foundation.earn.Reward
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientButtonSize
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.icon.GameController
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.VideoIcon
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal

@Composable
fun CheckInSection(
  checkInItems: List<CheckInGridItem>,
  canSignIn: Boolean,
  onSignInClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  Column(
    modifier = modifier,
    verticalArrangement = Arrangement.spacedBy(16.dp)
  ) {
    // 标题
    Text(
      text = strings.checkIn,
      style = MaterialTheme.typography.titleMedium.copy(
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.onSurface
      ),
    )

    Card(
      modifier = Modifier.fillMaxWidth(),
      shape = RoundedCornerShape(16.dp),
    ) {
      // 签到网格
      FlowRow(
        modifier = Modifier
          .fillMaxWidth()
          .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        verticalArrangement = Arrangement.spacedBy(10.dp),
        maxItemsInEachRow = 4
      ) {
        checkInItems.forEach { item ->
          CheckInGridItemView(item = item, modifier = Modifier.weight(1f))
        }
      }

      // 签到按钮
      GradientButton(
        text = strings.checkIn,
        onClick = onSignInClick,
        enabled = canSignIn,
        size = GradientButtonSize.Large,
        gradient = GradientColors.Purple,
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
          .fillMaxWidth()
          .padding(horizontal = 16.dp)
          .padding(bottom = 16.dp)
      )
    }
  }
}

@Composable
fun CheckInGridItemView(
  item: CheckInGridItem,
  modifier: Modifier = Modifier
) {
  when (item) {
    is CheckInGridItem.CheckInDay -> {
      CheckInDayItem(
        item = item,
        modifier = modifier
      )
    }

    is CheckInGridItem.ExternalLink -> {
      ExternalLinkItem(
        item = item,
        modifier = modifier
      )
    }
  }
}

@Composable
fun CheckInDayItem(
  item: CheckInGridItem.CheckInDay,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  Box(modifier = modifier) {
    // 基础卡片
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .aspectRatio(1f)
        .clip(RoundedCornerShape(8.dp))
        .background(MaterialTheme.colorScheme.surfaceVariant.brighten(.05f))
    ) {
      Column(
        modifier = Modifier.align(Alignment.Center),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterVertically)
      ) {
        // Day标题
        Text(
          text = strings.day(item.day),
          style = MaterialTheme.typography.titleSmall.copy(
            fontWeight = FontWeight.SemiBold,
          )
        )

        // 奖励
        Row(
          verticalAlignment = Alignment.CenterVertically,
          horizontalArrangement = Arrangement.spacedBy(2.dp),
          modifier = Modifier.basicMarquee()
        ) {
          Image(
            painter = painterResource(R.drawable.ic_cash_64px),
            contentDescription = null,
            modifier = Modifier.size(14.dp)
          )

          Text(
            text = "+${item.reward.reward.amountWithCommas}",
            style = MaterialTheme.typography.titleSmall.copy(
              fontWeight = FontWeight.SemiBold,
              fontSize = 12.sp,
              color = MaterialTheme.colorScheme.secondary.brighten()
            ),
          )
        }
      }
    }

    // 已签到蒙板 + 勾选标记
    if (item.isChecked) {
      Box(
        modifier = Modifier
          .matchParentSize()
          .background(
            color = Color.Black.copy(alpha = 0.6f),
            shape = RoundedCornerShape(8.dp)
          )
      ) {
        Icon(
          imageVector = Icons.Rounded.CheckCircle,
          contentDescription = "Checked",
          tint = MaterialTheme.colorScheme.primary.brighten(),
          modifier = Modifier
            .align(Alignment.Center)
            .size(40.dp)
        )
      }
    }

    // 今日标记
    if (item.isToday && !item.isChecked) {
      val dotColor = MaterialTheme.colorScheme.secondary
      Canvas(
        modifier = Modifier
          .align(Alignment.TopEnd)
          .offset(x = (-6).dp, y = 6.dp)
          .size(10.dp)
      ) {
        drawCircle(
          color = dotColor,
          radius = size.minDimension / 2
        )
      }
    }
  }
}

@Composable
fun ExternalLinkItem(
  item: CheckInGridItem.ExternalLink,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier
      .fillMaxWidth()
      .aspectRatio(1f)
      .clip(RoundedCornerShape(8.dp))
      .background(MaterialTheme.colorScheme.surfaceVariant.brighten(.05f))
      .clickable(onClick = item.onClick)
  ) {
    Image(
      imageVector = ValkyrieIcons.GameController,
      contentDescription = null,
      modifier = Modifier
        .matchParentSize()
        .padding(4.dp)
    )
  }
}

@Preview
@Composable
private fun CheckInSectionPreview() {
  AppTheme {
    CheckInSection(
      checkInItems = listOf(
        CheckInGridItem.CheckInDay(1, CheckInReward(1, Reward(BigDecimal("100"))), true, false),
        CheckInGridItem.CheckInDay(2, CheckInReward(2, Reward(BigDecimal("200"))), true, false),
        CheckInGridItem.CheckInDay(3, CheckInReward(3, Reward(BigDecimal("160"))), false, true),
        CheckInGridItem.CheckInDay(4, CheckInReward(4, Reward(BigDecimal("100"))), false, false),
        CheckInGridItem.CheckInDay(5, CheckInReward(5, Reward(BigDecimal("2000"))), false, false),
        CheckInGridItem.CheckInDay(6, CheckInReward(6, Reward(BigDecimal("2000"))), false, false),
        CheckInGridItem.CheckInDay(7, CheckInReward(7, Reward(BigDecimal("6000"))), false, false),
        CheckInGridItem.ExternalLink(ValkyrieIcons.VideoIcon) {}
      ),
      canSignIn = true,
      onSignInClick = {}
    )
  }
}