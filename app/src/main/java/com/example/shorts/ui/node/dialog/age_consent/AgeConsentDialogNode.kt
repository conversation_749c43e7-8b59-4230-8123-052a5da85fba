package com.example.shorts.ui.node.dialog.age_consent

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.content.Intent
import androidx.core.net.toUri
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.foundation.guia.DialogNode
import com.example.shorts.foundation.mmkv.UserSettingsKvStore
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.theme.AppTheme
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import android.app.Activity
import androidx.compose.foundation.Image
import androidx.compose.runtime.remember
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.android.findActivity
import com.example.shorts.foundation.android.openUrlInBrowser
import com.example.shorts.ui.common.button.GradientButtonSize
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.Welcome

private const val TAG = "AgeConsentDialog"

@Parcelize
class AgeConsentDialogNode : DialogNode(
  tag = TAG,
  dialogOptions = Dialog.DialogOptions(
    dismissOnBackPress = false,
    dismissOnClickOutside = false
  )
) {

  @Composable
  override fun Content(
    navigator: Navigator,
    dialog: Dialog?
  ) {
    val context = LocalContext.current
    val userSettingsKvStore: UserSettingsKvStore = koinInject()

    AgeConsentDialog(
      onAgree = {
        userSettingsKvStore.agreedToAgeConsent.value = true
        navigator.pop()
      },
      onClose = {
        // Exit the app if user doesn't agree
        context.findActivity().finish()
      }
    )
  }
}

@Composable
fun AgeConsentDialog(
  onAgree: () -> Unit,
  onClose: () -> Unit
) {
  val strings = LocalStrings.current
  val context = LocalContext.current
  Dialog(
    onDismissRequest = onClose,
    properties = DialogProperties(
      dismissOnBackPress = false,
      dismissOnClickOutside = false
    )
  ) {
    Card(
      modifier = Modifier
        .fillMaxWidth(),
      shape = RoundedCornerShape(20.dp),
    ) {
      Box(
        modifier = Modifier
          .fillMaxWidth()
          .padding(24.dp)
      ) {
        // Close button
        IconButton(
          onClick = onClose,
          modifier = Modifier
            .align(Alignment.TopEnd)
            .size(24.dp)
        ) {
          Icon(
            imageVector = Icons.Rounded.Close,
            contentDescription = "Close",
            tint = Color.White.copy(alpha = 0.6f),
            modifier = Modifier.size(20.dp)
          )
        }

        Column(
          modifier = Modifier.fillMaxWidth(),
          horizontalAlignment = Alignment.CenterHorizontally
        ) {
          Spacer(modifier = Modifier.height(16.dp))

          // Logo/Icon area with purple gradient background
          Image(
            imageVector = ValkyrieIcons.Welcome,
            contentDescription = "Logo",
            modifier = Modifier.size(100.dp)
          )

          Spacer(modifier = Modifier.height(12.dp))

          // Title
          Text(
            text = strings.welcomeToShortplay,
            color = Color.White,
            fontSize = 20.sp,
            lineHeight = 24.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
          )

          Spacer(modifier = Modifier.height(16.dp))

          // Description with clickable Privacy Policy
          val annotatedText = strings.ageConsentMessage {
            context.openUrlInBrowser("https://docs.google.com/document/d/16Agm9vzLmnpBhxw7J1O5RSVuYg1wTERDJZhnN1e4yHo/edit?usp=sharing")
          }

          Text(
            text = annotatedText,
            color = Color.White.copy(alpha = 0.8f),
            fontSize = 14.sp,
            lineHeight = 19.sp,
          )

          Spacer(modifier = Modifier.height(32.dp))

          // Agree button
          GradientButton(
            text = strings.agree,
            onClick = onAgree,
            size = GradientButtonSize.Custom(
              GradientButtonSize.Medium.config.copy(
                height = 48.dp,
                fontSize = 15.sp
              )
            ),
            modifier = Modifier
              .fillMaxWidth(),
            gradient = GradientColors.Purple,
            shape = CircleShape
          )

          Spacer(modifier = Modifier.height(8.dp))
        }
      }
    }
  }
}

@Preview
@Composable
fun AgeConsentDialogPreview() {
  AppTheme {
    AgeConsentDialog(
      onAgree = { },
      onClose = { }
    )
  }
}