package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import coil3.compose.AsyncImage
import dev.chrisbanes.haze.HazeDefaults
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import android.os.Parcelable
import androidx.compose.material.icons.rounded.PlayArrow
import androidx.compose.ui.draw.clip
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientButtonSize

enum class MoreListType {
  RECOMMENDED,
  NEW_RELEASES,
  CATEGORY
}

@Parcelize
data class MoreListConfig(
  val type: MoreListType,
  val category: ShortPlay.ShortPlayCategory? = null
) : Parcelable {
  companion object {
    fun recommended() = MoreListConfig(MoreListType.RECOMMENDED)
    fun newReleases() = MoreListConfig(MoreListType.NEW_RELEASES)
    fun category(category: ShortPlay.ShortPlayCategory) = MoreListConfig(
      type = MoreListType.CATEGORY,
      category = category
    )
  }
}

@Parcelize
class MoreListScreenNode(
  private val config: MoreListConfig
) : ScreenNode("more_list_screen") {

  // Backward compatibility constructors
  constructor(listType: MoreListType) : this(
    when (listType) {
      MoreListType.RECOMMENDED -> MoreListConfig.recommended()
      MoreListType.NEW_RELEASES -> MoreListConfig.newReleases()
      MoreListType.CATEGORY -> throw IllegalArgumentException("Category type requires categoryId and categoryName")
    }
  )

  @Composable
  override fun Content(navigator: Navigator) {
    val uiModel: MoreListUiModel = koinUiModel { parametersOf(config) }
    val uiState by uiModel.collectAsState()

    MoreListScreen(
      uiState = uiState,
      config = config,
      onBackClick = { navigator.pop() },
      onDramaClick = { drama ->
        navigator.push(DramaPlayScreenNode(drama))
      },
      onLoadMore = uiModel::loadMore,
      onRetry = uiModel::retry
    )
  }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MoreListScreen(
  uiState: MoreListUiState,
  config: MoreListConfig,
  onBackClick: () -> Unit,
  onDramaClick: (ShortPlay) -> Unit,
  onLoadMore: () -> Unit,
  onRetry: () -> Unit
) {
  val strings = LocalStrings.current
  val listState = rememberLazyListState()
  val hazeState = rememberHazeState()

  // Detect when user scrolls near the bottom to trigger pagination
  LaunchedEffect(listState) {
    snapshotFlow {
      listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index
    }.collect { lastVisibleIndex ->
      if (lastVisibleIndex != null &&
        lastVisibleIndex >= uiState.dramas.size - 3 &&
        !uiState.isLoadingMore &&
        uiState.hasMore
      ) {
        onLoadMore()
      }
    }
  }

  Scaffold(
    topBar = {
      TopAppBar(
        title = {
          Text(
            text = when (config.type) {
              MoreListType.RECOMMENDED -> strings.recommendedForYou
              MoreListType.NEW_RELEASES -> strings.newReleases
              MoreListType.CATEGORY -> config.category?.name ?: strings.category
            },
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium
          )
        },
        navigationIcon = {
          IconButton(onClick = onBackClick) {
            Icon(
              imageVector = Icons.AutoMirrored.Filled.ArrowBack,
              contentDescription = strings.back
            )
          }
        },
        modifier = Modifier.hazeEffect(
          state = hazeState,
          style = HazeDefaults.style(backgroundColor = MaterialTheme.colorScheme.surface),
        )
      )
    }
  ) { paddingValues ->
    when {
      uiState.isLoading && uiState.dramas.isEmpty() -> {
        Box(
          modifier = Modifier.fillMaxSize(),
          contentAlignment = Alignment.Center
        ) {
          CircularProgressIndicator()
        }
      }

      uiState.error != null && uiState.dramas.isEmpty() -> {
        Box(
          modifier = Modifier.fillMaxSize(),
          contentAlignment = Alignment.Center
        ) {
          Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
          ) {
            Text(
              text = uiState.error,
              style = MaterialTheme.typography.bodyLarge,
              color = MaterialTheme.colorScheme.error
            )
            Button(onClick = onRetry) {
              Text(strings.retry)
            }
          }
        }
      }

      else -> {
        LazyColumn(
          state = listState,
          modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .hazeSource(state = hazeState),
          contentPadding = PaddingValues(vertical = 16.dp),
          verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
          items(
            items = uiState.dramas,
            key = { drama -> drama.id }
          ) { drama ->
            DramaListItem(
              drama = drama,
              onClick = { onDramaClick(drama) },
              modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
            )
          }

          // Loading more indicator
          if (uiState.isLoadingMore) {
            item {
              Box(
                modifier = Modifier
                  .fillMaxWidth()
                  .padding(16.dp),
                contentAlignment = Alignment.Center
              ) {
                CircularProgressIndicator()
              }
            }
          }

          // End of list indicator
          if (!uiState.hasMore && uiState.dramas.isNotEmpty()) {
            item {
              Box(
                modifier = Modifier
                  .fillMaxWidth()
                  .padding(16.dp),
                contentAlignment = Alignment.Center
              ) {
                Text(
                  text = strings.noMoreContent,
                  style = MaterialTheme.typography.bodyMedium,
                  color = Color.Gray
                )
              }
            }
          }
        }
      }
    }
  }
}

@Composable
private fun DramaListItem(
  drama: ShortPlay,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Row(
    modifier = modifier
      .fillMaxWidth()
      .clip(RoundedCornerShape(4.dp))
      .clickable { onClick() },
    horizontalArrangement = Arrangement.spacedBy(12.dp)
  ) {
    // Drama cover image

    AsyncImage(
      model = drama.coverImage,
      contentDescription = drama.title,
      modifier = Modifier
        .size(width = 96.dp, height = 160.dp)
        .clip(RoundedCornerShape(12.dp)),
      contentScale = ContentScale.Crop
    )

    // Drama info
    Column(
      modifier = Modifier
        .weight(1f)
        .height(160.dp),
      verticalArrangement = Arrangement.SpaceBetween
    ) {
      Column(modifier = Modifier.weight(1f)) {
        Text(
          text = drama.title,
          fontSize = 16.sp,
          lineHeight = 20.sp,
          fontWeight = FontWeight.SemiBold,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
          color = Color.White
        )

        Spacer(modifier = Modifier.height(4.dp))

        Text(
          text = drama.desc ?: "",
          fontSize = 12.sp,
          lineHeight = 16.sp,
          overflow = TextOverflow.Ellipsis,
          color = Color.White.copy(alpha = 0.8f)
        )
      }

      // Bottom section with episode info and play button
      Row(
        modifier = Modifier
          .fillMaxWidth()
          .padding(top = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
      ) {
        Text(
          text = strings.episodeRange(drama.total),
          fontSize = 14.sp,
          lineHeight = 18.sp,
          color = Color.White.copy(alpha = 0.7f)
        )

        GradientButton(
          onClick = onClick,
          size = GradientButtonSize.Small,
          shape = RoundedCornerShape(8.dp),
          content = {
            Icon(
              imageVector = Icons.Rounded.PlayArrow,
              contentDescription = strings.play,
              tint = Color.White,
              modifier = Modifier.size(18.dp)
            )

            Text(
              text = strings.play,
              fontSize = 12.sp,
              lineHeight = 16.sp,
              color = Color.White,
              fontWeight = FontWeight.Medium,
              modifier = Modifier.padding(horizontal = 4.dp)
            )
          }
        )
      }
    }
  }
}
