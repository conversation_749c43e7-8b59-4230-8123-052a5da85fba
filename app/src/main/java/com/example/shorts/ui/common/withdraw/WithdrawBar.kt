package com.example.shorts.ui.common.withdraw

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.mmkv.WalletKvStore
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientButtonSize
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.node.screen.wallet.WalletBalance
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.earn.getWithdrawalOptions
import com.example.shorts.foundation.lyricist.runtimeLanguageTagFlow
import com.example.shorts.ui.theme.AppTheme
import org.koin.compose.koinInject
import java.math.BigDecimal

@Composable
fun WithdrawBar(
  onWithdrawClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val walletKvStore: WalletKvStore = koinInject()
  val balance by walletKvStore.balance.collectAsState()

  WithdrawBar(
    onWithdrawClick = onWithdrawClick,
    balance = balance,
    modifier = modifier
  )
}


@SuppressLint("ModifierParameter")
@Composable
fun WithdrawBar(
  onWithdrawClick: () -> Unit,
  balance: WalletBalance,
  targetWithdrawal: Withdrawal? = null,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  val currentLocale by runtimeLanguageTagFlow.collectAsState()
  val withdrawalOptions = remember(currentLocale) { getWithdrawalOptions(currentLocale) }
  val actualTargetWithdrawal = targetWithdrawal ?: withdrawalOptions.first()

  val progress = remember(
    balance,
    actualTargetWithdrawal
  ) { (balance.amount.toFloat() / actualTargetWithdrawal.amount.toFloat()).coerceIn(0f, 1f) }

  Surface(
    modifier = modifier,
    color = MaterialTheme.colorScheme.secondary.brighten().copy(.2f),
    shape = RoundedCornerShape(16.dp)
  ) {
    Row(
      modifier = Modifier.padding(16.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      Column(modifier = Modifier.weight(1f)) {
        Row(
          modifier = Modifier,
        ) {
          // Current balance
          Text(
            text = balance.displayAmount,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.alignByBaseline()
          )

          Spacer(modifier = Modifier.weight(1f))

          // Target amount
          Text(
            text = actualTargetWithdrawal.displayAmount,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(.85f),
            modifier = Modifier.alignByBaseline()
          )
        }

        // Progress bar
        Box(
          modifier = Modifier
            .padding(top = 8.dp)
            .fillMaxWidth()
            .height(10.dp)
        ) {
          // Background bar
          Box(
            modifier = Modifier
              .fillMaxSize()
              .clip(CircleShape)
              .background(MaterialTheme.colorScheme.onSecondary)
          )

          // Progress bar
          Box(
            modifier = Modifier
              .fillMaxHeight()
              .fillMaxWidth(progress)
              .clip(CircleShape)
              .background(
                GradientColors.Yellow
              )
          )
        }
      }

      Spacer(modifier = Modifier.width(20.dp))

      GradientButton(
        text = strings.withdraw,
        onClick = onWithdrawClick,
        modifier = Modifier,
        gradient = GradientColors.Yellow,
        shape = RoundedCornerShape(12.dp),
        contentColor = Color.Black,
        size = GradientButtonSize.Custom(GradientButtonSize.Medium.config.copy(fontSize = 15.sp))
      )
    }
  }

}


@Preview
@Composable
private fun WithdrawBarPreview() {
  AppTheme {
    WithdrawBar(
      onWithdrawClick = {},
      balance = WalletBalance(
        amount = BigDecimal("3464.25"),
        currencySymbol = "$",
        currencyCode = "USD"
      ),
      targetWithdrawal = null, // 使用默认值
      modifier = Modifier.fillMaxWidth()
    )
  }

}