package com.example.shorts.ui.node.screen.home.tasks

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.shorts.foundation.earn.Task
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.common.withdraw.WithdrawBar
import com.example.shorts.ui.node.screen.wallet.WalletNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun TasksScreen(
  navigator: Navigator,
  modifier: Modifier = Modifier,
  tasksUiModel: TasksUiModel = koinUiModel(),
  checkInUiModel: CheckInUiModel = koinUiModel()
) {
  val tasksUiState by tasksUiModel.collectAsState()
  val checkInUiState by checkInUiModel.collectAsState()

  TasksScreen(
    uiState = tasksUiState,
    checkInUiState = checkInUiState,
    onWithdrawClick = { navigator.push(WalletNode()) },
    onTaskClick = tasksUiModel::onTaskClick,
    onSignInClick = checkInUiModel::onSignInClick,
    modifier = modifier
  )
}

@Composable
fun TasksScreen(
  uiState: TasksUiState,
  checkInUiState: CheckInUiState,
  onWithdrawClick: () -> Unit,
  onTaskClick: (Task) -> Unit,
  onSignInClick: () -> Unit,
  modifier: Modifier = Modifier,
) {
  Column(modifier, verticalArrangement = Arrangement.spacedBy(16.dp)) {
    WithdrawBar(
      onWithdrawClick = onWithdrawClick,
      modifier = Modifier.padding(top = 16.dp).padding(horizontal = 16.dp)
    )

    CheckInSection(
      checkInItems = checkInUiState.checkInItems,
      canSignIn = checkInUiState.canSignIn,
      onSignInClick = onSignInClick,
      modifier = Modifier.padding(horizontal = 16.dp)
    )

//    MoreTasks(
//      onTaskClick = onTaskClick,
//      tasks = uiState.tasks,
//      modifier = Modifier.padding(horizontal = 16.dp)
//    )

    Spacer(modifier = Modifier.height(4.dp))
  }
}