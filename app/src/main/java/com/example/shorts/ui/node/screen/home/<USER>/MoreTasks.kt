package com.example.shorts.ui.node.screen.home.tasks

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.R
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.earn.Reward
import com.example.shorts.foundation.earn.Task
import com.example.shorts.foundation.earn.TaskInstance
import com.example.shorts.foundation.earn.TaskTemplate
import com.example.shorts.foundation.earn.TaskType
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientButtonSize
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal
import kotlin.collections.listOf

@Composable
fun MoreTasks(
  onTaskClick: (Task) -> Unit,
  tasks: List<Task>,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  Column(
    modifier = modifier
  ) {
    Text(
      text = strings.moreTasks,
      style = MaterialTheme.typography.titleMedium.copy(
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.onSurface
      ),
      modifier = Modifier.padding(bottom = 16.dp)
    )

    Card(
      modifier = Modifier.fillMaxWidth(),
      shape = RoundedCornerShape(16.dp),
    ) {
      tasks.forEach { task ->
        TaskItem(
          onClick = onTaskClick,
          task = task,
          modifier = Modifier.fillMaxWidth()
        )
      }
    }
  }
}

@Composable
fun TaskItem(
  onClick: (Task) -> Unit,
  task: Task,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp),
      verticalAlignment = Alignment.CenterVertically,
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      Image(
        imageVector = task.icon,
        contentDescription = null,
        modifier = Modifier.size(40.dp)
      )

      // Task info
      Column(
        modifier = Modifier.weight(1f)
      ) {
        Row(
          verticalAlignment = Alignment.CenterVertically
        ) {
          Image(
            painter = painterResource(R.drawable.ic_cash_64px),
            contentDescription = null,
            modifier = Modifier.size(16.dp)
          )

          Text(
            text = " +${task.reward.amountWithCommas}",
            style = MaterialTheme.typography.titleSmall.copy(
              fontWeight = FontWeight.SemiBold,
              color = MaterialTheme.colorScheme.secondary.brighten()
            ),
          )
        }

        Text(
          text = "${task.title} ${task.subtitle}",
          style = MaterialTheme.typography.bodySmall.copy(
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
          ),
//          modifier = Modifier.basicMarquee()
        )
      }

      GradientButton(
        text = task.buttonText,
        onClick = { onClick(task) },
        size = GradientButtonSize.Small,
        gradient = GradientColors.Purple,
        shape = CircleShape,
        modifier = Modifier.widthIn(min = 72.dp)
      )
    }
  }
}

@Preview
@Composable
private fun TaskItemPreview() {
  AppTheme {
    TaskItem(
      onClick = {},
      task = Task(
        template = TaskTemplate(
          id = "watch_ads_3",
          type = TaskType.WatchAds,
          goal = 3,
          reward = Reward(BigDecimal("100")),
          buttonText = "30s"
        ),
        instance = TaskInstance(
          templateId = "watch_ads_3",
          progress = 0,
          enabled = true,
          rewardClaimed = false
        )
      ),
    )
  }
}

@Preview
@Composable
private fun MoreTasksPreview() {
  AppTheme {
    MoreTasks(
      onTaskClick = {},
      tasks = listOf(
        Task(
          template = TaskTemplate(
            "watch_ads_3",
            TaskType.WatchAds,
            3,
            Reward(BigDecimal("100")),
            "30s"
          ),
          instance = TaskInstance("watch_ads_3", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_ads_10",
            TaskType.WatchAds,
            10,
            Reward(BigDecimal("100")),
            "Watch"
          ),
          instance = TaskInstance("watch_ads_10", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_ads_20",
            TaskType.WatchAds,
            20,
            Reward(BigDecimal("100")),
            "Watch"
          ),
          instance = TaskInstance("watch_ads_20", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_drama_15",
            TaskType.WatchDrama,
            15,
            Reward(BigDecimal("100")),
            "Go"
          ),
          instance = TaskInstance("watch_drama_15", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_drama_30",
            TaskType.WatchDrama,
            30,
            Reward(BigDecimal("100")),
            "Go"
          ),
          instance = TaskInstance("watch_drama_30", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_drama_50",
            TaskType.WatchDrama,
            50,
            Reward(BigDecimal("500")),
            "Go"
          ),
          instance = TaskInstance("watch_drama_50", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_drama_80",
            TaskType.WatchDrama,
            80,
            Reward(BigDecimal("200")),
            "Go"
          ),
          instance = TaskInstance("watch_drama_80", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_drama_100",
            TaskType.WatchDrama,
            100,
            Reward(BigDecimal("200")),
            "Go"
          ),
          instance = TaskInstance("watch_drama_100", 0, true, false)
        ),
        Task(
          template = TaskTemplate(
            "watch_drama_200",
            TaskType.WatchDrama,
            200,
            Reward(BigDecimal("2000")),
            "Go"
          ),
          instance = TaskInstance("watch_drama_200", 0, true, false)
        )
      )
    )
  }
}

