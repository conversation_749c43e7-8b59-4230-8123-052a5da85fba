package com.example.shorts.ui.node.screen.home

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.foundation.android.brighten
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.HomeFilled
import com.example.shorts.ui.icon.HomeOutlined
import com.example.shorts.ui.icon.VideoFilled
import com.example.shorts.ui.icon.VideoOutlined
import com.example.shorts.ui.icon.GamepadFilled
import com.example.shorts.ui.icon.GamepadOutlined
import com.example.shorts.ui.icon.GiftFilled
import com.example.shorts.ui.icon.GiftOutlined
import com.example.shorts.ui.theme.AppTheme

@SuppressLint("ModifierParameter")
@Composable
fun HomeBottomBar(
  selectedTab: HomeTab,
  onTabSelected: (HomeTab) -> Unit = {},
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  Box(
    modifier = modifier
      .fillMaxWidth()
      .navigationBarsPadding()
  ) {
    Row(
      modifier = Modifier.fillMaxWidth(),
      verticalAlignment = Alignment.CenterVertically
    ) {
      BottomBarItem(
        icon = if (selectedTab == HomeTab.Discover) ValkyrieIcons.HomeFilled else ValkyrieIcons.HomeOutlined,
        label = strings.discover,
        isSelected = selectedTab == HomeTab.Discover,
        onClick = { onTabSelected(HomeTab.Discover) },
        modifier = Modifier.weight(1f)
      )

      BottomBarItem(
        icon = if (selectedTab == HomeTab.Shorts) ValkyrieIcons.VideoFilled else ValkyrieIcons.VideoOutlined,
        label = strings.shorts,
        isSelected = selectedTab == HomeTab.Shorts,
        onClick = { onTabSelected(HomeTab.Shorts) },
        modifier = Modifier.weight(1f)
      )

//      BottomBarItem(
//        icon = if (selectedTab == HomeTab.Games) ValkyrieIcons.GamepadFilled else ValkyrieIcons.GamepadOutlined,
//        label = "Game",
//        isSelected = selectedTab == HomeTab.Games,
//        onClick = { onTabSelected(HomeTab.Games) },
//        modifier = Modifier.weight(1f)
//      )

      BottomBarItem(
        icon = if (selectedTab == HomeTab.Tasks) ValkyrieIcons.GiftFilled else ValkyrieIcons.GiftOutlined,
        label = strings.task,
        isSelected = selectedTab == HomeTab.Tasks,
        onClick = { onTabSelected(HomeTab.Tasks) },
        modifier = Modifier.weight(1f)
      )
    }
  }
}

@Composable
private fun BottomBarItem(
  icon: ImageVector,
  label: String,
  isSelected: Boolean,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  Box(modifier = modifier.clickable { onClick() }, contentAlignment = Alignment.Center) {
    Column(
      horizontalAlignment = Alignment.CenterHorizontally,
      verticalArrangement = Arrangement.spacedBy(2.dp),
      modifier = Modifier.padding(bottom = 10.dp, top = 8.dp)
    ) {
      Image(
        imageVector = icon,
        contentDescription = label,
        modifier = Modifier.size(30.dp),
      )

      Text(
        text = label,
        fontSize = 11.sp,
        fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
        lineHeight = 14.sp,
        color = if (isSelected) {
          MaterialTheme.colorScheme.primary.brighten()
        } else {
          MaterialTheme.colorScheme.onSurfaceVariant
        }
      )
    }
  }
}

@Preview
@Composable
private fun HomeBottomBarPreview() {
  AppTheme {
    var selectedTab by remember { mutableStateOf<HomeTab>(HomeTab.Shorts) }
    HomeBottomBar(
      selectedTab = selectedTab,
      onTabSelected = { selectedTab = it }
    )
  }
}