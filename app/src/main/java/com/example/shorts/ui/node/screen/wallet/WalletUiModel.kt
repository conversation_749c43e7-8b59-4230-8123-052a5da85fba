package com.example.shorts.ui.node.screen.wallet

import androidx.compose.ui.graphics.Color
import com.example.shorts.foundation.guia.GlobalNavigator
import com.example.shorts.foundation.lyricist.globalAppStrings
import com.example.shorts.foundation.mmkv.WalletKvStore
import com.example.shorts.foundation.mvi_ui_model.UiModel
import com.example.shorts.foundation.toast.showToast
import com.example.shorts.ui.node.dialog.confirm_withdrawal.ConfirmWithdrawalDialogNode
import com.example.shorts.ui.node.dialog.edit_wallet_address.EditWalletAddressDialogNode
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.earn.getWithdrawalOptions
import com.example.shorts.foundation.lyricist.runtimeLanguageTagFlow
import com.example.shorts.foundation.number.formatWithCommas
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.replaceLast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class WalletUiModel(
  private val walletKvStore: WalletKvStore,
) : UiModel<WalletUiState, WalletSideEffect>(WalletUiState()) {

  init {
    configure()
  }

  private fun configure() = intent {
    registerWalletBalanceFlow()
    loadWithdrawalOptions()
  }

  private fun loadWithdrawalOptions() = intent {
    val currentLocale = runtimeLanguageTagFlow.value
    val withdrawOptions = getWithdrawalOptions(currentLocale)
    reduce { state.copy(withdrawals = withdrawOptions) }
  }

  private suspend fun isNetworkAvailable(): Boolean = withContext(Dispatchers.IO) {
    try {
      val url = java.net.URL("https://bing.com")
      val connection = url.openConnection() as java.net.HttpURLConnection
      connection.connectTimeout = 5000
      connection.readTimeout = 5000
      connection.requestMethod = "HEAD"
      connection.responseCode == java.net.HttpURLConnection.HTTP_OK
    } catch (_: Exception) {
      false
    }
  }

  /**
   * 选择提现金额
   */
  fun selectWithdrawal(withdrawal: Withdrawal) = intent {
    reduce { state.copy(selectedWithdrawal = withdrawal) }
  }

  fun onPerformWithdraw() = intent {
    val withdrawal = state.selectedWithdrawal
    if (withdrawal == null) {
      showToast { "Please select a withdrawal amount" }
      return@intent
    }

    onPerformWithdraw(withdrawal)
  }

  /**
   * 执行提现
   */
  fun onPerformWithdraw(withdrawal: Withdrawal) = intent {
    if (withdrawal.amount > state.balance.amount) {
      showToast { "Insufficient balance" }
      return@intent
    }

    // 检查最小提现金额
    val currentLocale = runtimeLanguageTagFlow.value
    val withdrawOptions = getWithdrawalOptions(currentLocale)
    val minimumWithdraw = withdrawOptions.first().amount
    if (withdrawal.amount < minimumWithdraw) {
      showToast(
        globalAppStrings.withdrawalNoReachTips(
          "${state.balance.currencySymbol}${minimumWithdraw.formatWithCommas()}",
          "${state.balance.currencySymbol}${
            minimumWithdraw.subtract(withdrawal.amount).formatWithCommas()
          }",
          Color.White
        ).toString()
      )

      return@intent
    }

    try {
      reduce { state.copy(isLoading = true) }

      // 检查网络连接
      if (!isNetworkAvailable()) {
        showToast("Network connection is not available, please check network and try again")
        delay(500)
        reduce { state.copy(isLoading = false) }
        return@intent
      }

      val newAmount = walletKvStore.amount.value - withdrawal.amount.toDouble()
      walletKvStore.amount.value = newAmount

      val newBalance = state.balance.copy(
        amount = newAmount.toBigDecimal()
      )

      reduce {
        state.copy(
          balance = newBalance,
          selectedWithdrawal = null,
        )
      }

      postSideEffect(WalletSideEffect.WithdrawSuccessful(withdrawal))

      delay(700)
      reduce { state.copy(isLoading = false) }
    } catch (e: Exception) {
      reduce { state.copy(isLoading = false) }
      showToast(e.message ?: "Withdrawal failed")
    }
  }

  /**
   * 注册监听钱包数据
   */
  fun registerWalletBalanceFlow() = intent {
    walletKvStore.balance.onEach { updatedBalance ->
      reduce { state.copy(balance = updatedBalance) }
    }.launchIn(uiModelScope)
  }

  fun onCashOutClick() = intent {
    val withdrawal = state.selectedWithdrawal ?: return@intent

    val confirmWithdrawalDialogNode = ConfirmWithdrawalDialogNode(withdrawal, this@WalletUiModel)

    if (walletKvStore.walletAddress.first().isEmpty()) {
      val editWalletAddressDialogNode = EditWalletAddressDialogNode(
        confirmAction = {
          GlobalNavigator.tryTransaction { replaceLast(confirmWithdrawalDialogNode) }
        }
      )

      GlobalNavigator.transaction { push(editWalletAddressDialogNode) }
    } else {
      GlobalNavigator.transaction { push(confirmWithdrawalDialogNode) }
    }
  }

}